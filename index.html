<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hệ thống Quản l<PERSON>ản xuất May mặc</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1><i class="fas fa-tshirt"></i> Quản lý Sản xuất May mặc</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openModal('orderModal')">
                    <i class="fas fa-plus"></i> Tạo đơn hàng
                </button>
                <button class="btn btn-success" onclick="openModal('planModal')">
                    <i class="fas fa-clipboard-list"></i> Kế hoạch vật tư
                </button>
                <button class="btn btn-warning" onclick="openModal('approvalModal')">
                    <i class="fas fa-check-circle"></i> Phê duyệt
                </button>
                <button class="btn btn-secondary" onclick="openModal('materialModal')">
                    <i class="fas fa-boxes"></i> Quản lý vật tư
                </button>
                <button class="btn btn-info" onclick="showReports()">
                    <i class="fas fa-chart-bar"></i> Báo cáo
                </button>
            </div>
        </div>
    </header>

    <!-- Dashboard Stats -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
            <div class="stat-info">
                <h3 id="totalOrders">0</h3>
                <p>Tổng đơn hàng</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-clock"></i></div>
            <div class="stat-info">
                <h3 id="inProgress">0</h3>
                <p>Đang sản xuất</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
            <div class="stat-info">
                <h3 id="completed">0</h3>
                <p>Hoàn thành</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
            <div class="stat-info">
                <h3 id="delayed">0</h3>
                <p>Trễ hạn</p>
            </div>
        </div>
    </div>

    <!-- Kanban Board -->
    <div class="kanban-container">
        <div class="kanban-column" data-status="new">
            <div class="column-header">
                <h3><i class="fas fa-file-alt"></i> Đơn hàng mới</h3>
                <span class="count" id="count-new">0</span>
            </div>
            <div class="column-content" id="column-new"></div>
        </div>

        <div class="kanban-column" data-status="planning">
            <div class="column-header">
                <h3><i class="fas fa-clipboard-list"></i> Lập kế hoạch</h3>
                <span class="count" id="count-planning">0</span>
            </div>
            <div class="column-content" id="column-planning"></div>
        </div>

        <div class="kanban-column" data-status="approved">
            <div class="column-header">
                <h3><i class="fas fa-check-circle"></i> Đã duyệt</h3>
                <span class="count" id="count-approved">0</span>
            </div>
            <div class="column-content" id="column-approved"></div>
        </div>

        <div class="kanban-column" data-status="cutting">
            <div class="column-header">
                <h3><i class="fas fa-cut"></i> Tổ Cắt</h3>
                <span class="count" id="count-cutting">0</span>
            </div>
            <div class="column-content" id="column-cutting"></div>
        </div>

        <div class="kanban-column" data-status="printing">
            <div class="column-header">
                <h3><i class="fas fa-print"></i> In/Thêu</h3>
                <span class="count" id="count-printing">0</span>
            </div>
            <div class="column-content" id="column-printing"></div>
        </div>

        <div class="kanban-column" data-status="sewing">
            <div class="column-header">
                <h3><i class="fas fa-tshirt"></i> Tổ May</h3>
                <span class="count" id="count-sewing">0</span>
            </div>
            <div class="column-content" id="column-sewing"></div>
        </div>

        <div class="kanban-column" data-status="qc">
            <div class="column-header">
                <h3><i class="fas fa-search"></i> QC</h3>
                <span class="count" id="count-qc">0</span>
            </div>
            <div class="column-content" id="column-qc"></div>
        </div>

        <div class="kanban-column" data-status="completed">
            <div class="column-header">
                <h3><i class="fas fa-check"></i> Hoàn thành</h3>
                <span class="count" id="count-completed">0</span>
            </div>
            <div class="column-content" id="column-completed"></div>
        </div>
    </div>

    <!-- Modal: Tạo đơn hàng -->
    <div id="orderModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tạo đơn hàng mới</h2>
                <span class="close" onclick="closeModal('orderModal')">&times;</span>
            </div>
            <form id="orderForm">
                <div class="form-group">
                    <label>Mã đơn hàng:</label>
                    <input type="text" id="orderCode" required>
                </div>
                <div class="form-group">
                    <label>Khách hàng:</label>
                    <input type="text" id="customerName" required>
                </div>
                <div class="form-group">
                    <label>Sản phẩm:</label>
                    <select id="productType" required>
                        <option value="">Chọn sản phẩm</option>
                        <option value="ao-thun">Áo thun</option>
                        <option value="ao-polo">Áo polo</option>
                        <option value="ao-hoodie">Áo hoodie</option>
                        <option value="quan-jean">Quần jean</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Số lượng:</label>
                    <input type="number" id="quantity" required min="1">
                </div>
                <div class="form-group">
                    <label>Ngày giao:</label>
                    <input type="date" id="deliveryDate" required>
                </div>
                <div class="form-group">
                    <label>Ghi chú:</label>
                    <textarea id="notes" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('orderModal')">Hủy</button>
                    <button type="submit" class="btn btn-primary">Tạo đơn hàng</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal: Quản lý vật tư -->
    <div id="materialModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Quản lý vật tư</h2>
                <span class="close" onclick="closeModal('materialModal')">&times;</span>
            </div>
            <div class="material-tabs">
                <button class="tab-btn active" onclick="showTab('inventory')">Tồn kho</button>
                <button class="tab-btn" onclick="showTab('suppliers')">Nhà cung cấp</button>
                <button class="tab-btn" onclick="showTab('bom')">BOM</button>
            </div>
            <div id="materialContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Modal: Kế hoạch vật tư -->
    <div id="planModal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>Kế hoạch vật tư - Phòng Kinh Doanh</h2>
                <span class="close" onclick="closeModal('planModal')">&times;</span>
            </div>
            <div class="plan-tabs">
                <button class="tab-btn active" onclick="showPlanTab('pending')">Chờ lập kế hoạch</button>
                <button class="tab-btn" onclick="showPlanTab('planned')">Đã lập kế hoạch</button>
                <button class="tab-btn" onclick="showPlanTab('materials')">Tổng hợp vật tư</button>
            </div>
            <div id="planContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Modal: Phê duyệt -->
    <div id="approvalModal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>Phê duyệt công đoạn</h2>
                <span class="close" onclick="closeModal('approvalModal')">&times;</span>
            </div>
            <div class="approval-tabs">
                <button class="tab-btn active" onclick="showApprovalTab('pending')">Chờ phê duyệt</button>
                <button class="tab-btn" onclick="showApprovalTab('approved')">Đã phê duyệt</button>
                <button class="tab-btn" onclick="showApprovalTab('rejected')">Từ chối</button>
            </div>
            <div id="approvalContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Modal: Chi tiết đơn hàng -->
    <div id="orderDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Chi tiết đơn hàng</h2>
                <span class="close" onclick="closeModal('orderDetailModal')">&times;</span>
            </div>
            <div id="orderDetailContent">
                <!-- Content will be loaded dynamically -->
            </div>
        </div>
    </div>

    <script src="js/data.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
