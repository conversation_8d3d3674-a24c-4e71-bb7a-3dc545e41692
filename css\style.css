/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: #2c3e50;
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 1000;
}

.sidebar.collapsed {
    transform: translateX(-280px);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

.mobile-toggle {
    display: none;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: #34495e;
    color: white;
}

.nav-link.active {
    background: #3498db;
    color: white;
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-link span {
    flex: 1;
}

.submenu-arrow {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.nav-item.open .submenu-arrow {
    transform: rotate(180deg);
}

/* Submenu */
.submenu {
    list-style: none;
    margin: 0;
    padding: 0;
    background: #1a252f;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.nav-item.open .submenu {
    max-height: 300px;
}

.submenu li a {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.5rem 0.5rem 3rem;
    color: #95a5a6;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.submenu li a:hover {
    background: #2c3e50;
    color: white;
}

.submenu li a i {
    width: 16px;
    margin-right: 0.5rem;
    font-size: 0.8rem;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    transition: margin-left 0.3s ease;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content.expanded {
    margin-left: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 0;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Stats Container */
.stats-container {
    margin: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #4CAF50; }
.stat-card:nth-child(2) .stat-icon { background: #FF9800; }
.stat-card:nth-child(3) .stat-icon { background: #2196F3; }
.stat-card:nth-child(4) .stat-icon { background: #f44336; }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* Kanban Board */
.kanban-container {
    margin: 2rem;
    display: flex;
    gap: 1.5rem;
    overflow-x: auto;
    min-height: 600px;
}

.kanban-column {
    min-width: 280px;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

.column-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
}

.count {
    background: #6c757d;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.column-content {
    min-height: 500px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Order Cards */
.order-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.order-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.order-code {
    font-weight: 600;
    color: #007bff;
    font-size: 0.9rem;
}

.priority {
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
}

.priority.high { background: #ffebee; color: #c62828; }
.priority.medium { background: #fff3e0; color: #ef6c00; }
.priority.low { background: #e8f5e8; color: #2e7d32; }

.customer-name {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.product-info {
    font-size: 0.85rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.quantity {
    font-weight: 600;
    color: #007bff;
}

.order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
}

.delivery-date {
    font-size: 0.8rem;
    color: #666;
}

.assigned-to {
    font-size: 0.8rem;
    background: #e9ecef;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    color: #495057;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    margin: 0.5rem 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
    padding: 0 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.form-actions {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Tabs */
.material-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.tab-btn:hover {
    background: #f8f9fa;
}

/* Responsive */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-280px);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .kanban-container {
        flex-direction: column;
        margin: 1rem;
    }

    .kanban-column {
        min-width: auto;
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        margin: 1rem;
    }

    .large-modal .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}

/* Drag and Drop */
.column-content.drag-over {
    background: #e3f2fd;
    border: 2px dashed #2196F3;
}

/* Order Detail Styles */
.order-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.detail-section {
    margin-bottom: 2rem;
}

.detail-section h4 {
    color: #495057;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.detail-grid > div {
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #007bff;
}

/* BOM Table */
.bom-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.bom-table th,
.bom-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.bom-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.bom-table tr:hover {
    background: #f8f9fa;
}

/* Assign Section */
.assign-section {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.assign-section select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
}

/* Inventory Table */
.inventory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.inventory-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.inventory-table th,
.inventory-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.inventory-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.inventory-table tr:hover {
    background: #f8f9fa;
}

.inventory-table tr.low-stock {
    background: #fff5f5;
}

.status-warning {
    color: #dc3545;
    font-weight: 600;
}

.status-ok {
    color: #28a745;
    font-weight: 600;
}

/* Suppliers Grid */
.suppliers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.suppliers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.supplier-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.supplier-card h5 {
    color: #007bff;
    margin-bottom: 1rem;
}

.supplier-card p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.supplier-materials ul {
    margin: 0.5rem 0 0 1rem;
    font-size: 0.85rem;
}

/* BOM Templates */
.bom-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.bom-template-card {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.bom-template-card h5 {
    color: #28a745;
    margin-bottom: 1rem;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 2000;
}

.notification.show {
    transform: translateX(0);
}

.notification i {
    font-size: 1.2rem;
}

/* Button sizes */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Large Modal */
.large-modal {
    max-width: 1000px;
}

/* Plan/Approval Tabs */
.plan-tabs, .approval-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

/* Plan Order Cards */
.plan-order-card, .approval-order-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.plan-order-header, .approval-order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.plan-order-header h5, .approval-order-header h5 {
    margin: 0;
    color: #007bff;
}

.plan-order-info, .approval-order-info {
    margin-bottom: 1rem;
}

.plan-order-info p, .approval-order-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
}

.plan-order-actions, .approval-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* Material Plan Summary */
.material-plan-summary, .material-plan-detail {
    margin: 1rem 0;
}

.material-plan-summary h6, .material-plan-detail h6 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.mini-table, .approval-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.mini-table th, .mini-table td,
.approval-table th, .approval-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.mini-table th, .approval-table th {
    background: #f8f9fa;
    font-weight: 600;
}

.mini-table tr.shortage, .approval-table tr.shortage {
    background: #fff5f5;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.draft {
    background: #fff3cd;
    color: #856404;
}

.status-badge.pending_approval {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.approved {
    background: #d4edda;
    color: #155724;
}

.status-badge.rejected {
    background: #f8d7da;
    color: #721c24;
}

/* Pending Orders Grid */
.pending-orders, .planned-orders, .approval-orders {
    display: grid;
    gap: 1rem;
}

/* Department Dashboard */
.department-dashboard {
    padding: 0;
}

.department-content {
    margin: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.content-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-section h3 {
    margin-bottom: 1.5rem;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* Order/Task Lists */
.order-list, .task-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-item, .task-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #007bff;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-info, .task-info {
    flex: 1;
}

.order-info h4, .task-info h4 {
    margin: 0 0 0.5rem 0;
    color: #007bff;
    font-size: 1rem;
}

.order-info p, .task-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: #666;
}

.order-actions, .task-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Inventory Dashboard */
.inventory-dashboard {
    padding: 0;
}

.inventory-content {
    margin: 2rem;
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inventory-content h3 {
    margin-bottom: 1.5rem;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* Dashboard Placeholder */
.dashboard-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: white;
    margin: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-placeholder h3 {
    color: #6c757d;
    font-style: italic;
}

/* Text utilities */
.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

/* Animation */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease;
}
