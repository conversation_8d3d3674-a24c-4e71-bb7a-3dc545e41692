// Biến global
let currentData = getData();
let draggedElement = null;

// Khởi tạo ứng dụng
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    updateStats();
    renderKanbanBoard();
    setupEventListeners();
}

// Cập nhật thống kê
function updateStats() {
    const orders = currentData.orders;
    const totalOrders = orders.length;
    const inProgress = orders.filter(o => ['cutting', 'printing', 'sewing', 'qc'].includes(o.status)).length;
    const completed = orders.filter(o => o.status === 'completed').length;
    const delayed = orders.filter(o => new Date(o.deliveryDate) < new Date() && o.status !== 'completed').length;

    document.getElementById('totalOrders').textContent = totalOrders;
    document.getElementById('inProgress').textContent = inProgress;
    document.getElementById('completed').textContent = completed;
    document.getElementById('delayed').textContent = delayed;
}

// Render Kanban Board
function renderKanbanBoard() {
    const statuses = ['new', 'cutting', 'printing', 'sewing', 'qc', 'completed'];
    
    statuses.forEach(status => {
        const orders = currentData.orders.filter(order => order.status === status);
        const columnContent = document.getElementById(`column-${status}`);
        const countElement = document.getElementById(`count-${status}`);
        
        // Cập nhật số lượng
        countElement.textContent = orders.length;
        
        // Render cards
        columnContent.innerHTML = '';
        orders.forEach(order => {
            const card = createOrderCard(order);
            columnContent.appendChild(card);
        });
    });
}

// Tạo card đơn hàng
function createOrderCard(order) {
    const card = document.createElement('div');
    card.className = 'order-card';
    card.draggable = true;
    card.dataset.orderId = order.id;
    
    const priorityClass = order.priority;
    const isDelayed = new Date(order.deliveryDate) < new Date() && order.status !== 'completed';
    
    card.innerHTML = `
        <div class="order-header">
            <span class="order-code">${order.code}</span>
            <span class="priority ${priorityClass}">${getPriorityText(order.priority)}</span>
        </div>
        <div class="customer-name">${order.customer}</div>
        <div class="product-info">
            <strong>${order.product}</strong> - 
            <span class="quantity">${order.quantity} sản phẩm</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: ${order.progress}%"></div>
        </div>
        <div class="order-footer">
            <span class="delivery-date ${isDelayed ? 'text-danger' : ''}">
                <i class="fas fa-calendar"></i> ${formatDate(order.deliveryDate)}
            </span>
            ${order.assignedTo ? `<span class="assigned-to">${order.assignedTo}</span>` : ''}
        </div>
    `;
    
    // Thêm event listeners
    card.addEventListener('click', () => showOrderDetail(order.id));
    card.addEventListener('dragstart', handleDragStart);
    card.addEventListener('dragend', handleDragEnd);
    
    return card;
}

// Xử lý drag and drop
function handleDragStart(e) {
    draggedElement = e.target;
    e.target.classList.add('dragging');
}

function handleDragEnd(e) {
    e.target.classList.remove('dragging');
    draggedElement = null;
}

// Setup event listeners
function setupEventListeners() {
    // Form submit
    document.getElementById('orderForm').addEventListener('submit', handleOrderSubmit);
    
    // Drag and drop cho columns
    const columns = document.querySelectorAll('.column-content');
    columns.forEach(column => {
        column.addEventListener('dragover', handleDragOver);
        column.addEventListener('drop', handleDrop);
        column.addEventListener('dragenter', handleDragEnter);
        column.addEventListener('dragleave', handleDragLeave);
    });
}

function handleDragOver(e) {
    e.preventDefault();
}

function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.target.classList.remove('drag-over');
    
    if (draggedElement) {
        const orderId = draggedElement.dataset.orderId;
        const newStatus = e.target.closest('.kanban-column').dataset.status;
        
        updateOrderStatus(orderId, newStatus);
    }
}

// Cập nhật trạng thái đơn hàng
function updateOrderStatus(orderId, newStatus) {
    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        currentData.orders[orderIndex].status = newStatus;
        
        // Cập nhật progress dựa trên status
        const progressMap = {
            'new': 0,
            'cutting': 20,
            'printing': 40,
            'sewing': 60,
            'qc': 80,
            'completed': 100
        };
        currentData.orders[orderIndex].progress = progressMap[newStatus] || 0;
        
        saveData(currentData);
        updateStats();
        renderKanbanBoard();
        
        // Hiển thị thông báo
        showNotification(`Đã chuyển đơn hàng ${currentData.orders[orderIndex].code} sang ${getStatusText(newStatus)}`);
    }
}

// Xử lý submit form tạo đơn hàng
function handleOrderSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const newOrder = {
        id: 'DH' + String(Date.now()).slice(-6),
        code: formData.get('orderCode') || 'DH' + String(Date.now()).slice(-6),
        customer: formData.get('customerName'),
        product: getProductName(formData.get('productType')),
        quantity: parseInt(formData.get('quantity')),
        deliveryDate: formData.get('deliveryDate'),
        status: 'new',
        priority: 'medium',
        assignedTo: '',
        progress: 0,
        notes: formData.get('notes') || '',
        createdDate: new Date().toISOString().split('T')[0],
        bom: generateBOM(formData.get('productType'), parseInt(formData.get('quantity')))
    };
    
    currentData.orders.push(newOrder);
    saveData(currentData);
    
    // Reset form và đóng modal
    e.target.reset();
    closeModal('orderModal');
    
    // Cập nhật giao diện
    updateStats();
    renderKanbanBoard();
    
    showNotification(`Đã tạo đơn hàng ${newOrder.code} thành công!`);
}

// Tạo BOM cho đơn hàng
function generateBOM(productType, quantity) {
    const template = currentData.bomTemplates[productType];
    if (!template) return [];

    return template.map(item => ({
        material: item.material,
        quantity: item.quantity * quantity,
        unit: item.unit
    }));
}

// Utility functions
function getPriorityText(priority) {
    const priorityMap = {
        'high': 'Cao',
        'medium': 'Trung bình',
        'low': 'Thấp'
    };
    return priorityMap[priority] || priority;
}

function getStatusText(status) {
    const statusMap = {
        'new': 'Đơn hàng mới',
        'cutting': 'Tổ cắt',
        'printing': 'In/Thêu',
        'sewing': 'Tổ may',
        'qc': 'QC',
        'completed': 'Hoàn thành'
    };
    return statusMap[status] || status;
}

function getProductName(productType) {
    const productMap = {
        'ao-thun': 'Áo thun',
        'ao-polo': 'Áo polo',
        'ao-hoodie': 'Áo hoodie',
        'quan-jean': 'Quần jean'
    };
    return productMap[productType] || productType;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
}

// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Khởi tạo tab mặc định cho modal vật tư
    if (modalId === 'materialModal') {
        setTimeout(() => {
            document.querySelector('.tab-btn.active').click();
        }, 100);
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Đóng modal khi click outside
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
}

// Hiển thị chi tiết đơn hàng
function showOrderDetail(orderId) {
    const order = currentData.orders.find(o => o.id === orderId);
    if (!order) return;

    const content = document.getElementById('orderDetailContent');
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="order-detail-header">
                <h3>Đơn hàng: ${order.code}</h3>
                <span class="priority ${order.priority}">${getPriorityText(order.priority)}</span>
            </div>

            <div class="detail-section">
                <h4>Thông tin cơ bản</h4>
                <div class="detail-grid">
                    <div><strong>Khách hàng:</strong> ${order.customer}</div>
                    <div><strong>Sản phẩm:</strong> ${order.product}</div>
                    <div><strong>Số lượng:</strong> ${order.quantity}</div>
                    <div><strong>Ngày giao:</strong> ${formatDate(order.deliveryDate)}</div>
                    <div><strong>Trạng thái:</strong> ${getStatusText(order.status)}</div>
                    <div><strong>Tiến độ:</strong> ${order.progress}%</div>
                </div>
            </div>

            <div class="detail-section">
                <h4>BOM - Định mức vật tư</h4>
                <table class="bom-table">
                    <thead>
                        <tr>
                            <th>Vật tư</th>
                            <th>Số lượng</th>
                            <th>Đơn vị</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.bom.map(item => `
                            <tr>
                                <td>${item.material}</td>
                                <td>${item.quantity}</td>
                                <td>${item.unit}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="detail-section">
                <h4>Giao việc</h4>
                <div class="assign-section">
                    <select id="assignWorker" class="form-control">
                        <option value="">Chọn nhân viên/đối tác</option>
                        ${currentData.workers.map(worker => `
                            <option value="${worker.name}" ${order.assignedTo === worker.name ? 'selected' : ''}>
                                ${worker.name} - ${worker.role}
                            </option>
                        `).join('')}
                    </select>
                    <button class="btn btn-primary" onclick="assignWorker('${order.id}')">
                        Giao việc
                    </button>
                </div>
            </div>

            ${order.notes ? `
                <div class="detail-section">
                    <h4>Ghi chú</h4>
                    <p>${order.notes}</p>
                </div>
            ` : ''}
        </div>
    `;

    openModal('orderDetailModal');
}

// Giao việc cho nhân viên
function assignWorker(orderId) {
    const workerSelect = document.getElementById('assignWorker');
    const selectedWorker = workerSelect.value;

    if (!selectedWorker) {
        alert('Vui lòng chọn nhân viên/đối tác');
        return;
    }

    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        currentData.orders[orderIndex].assignedTo = selectedWorker;
        saveData(currentData);
        renderKanbanBoard();
        showNotification(`Đã giao việc cho ${selectedWorker}`);
        closeModal('orderDetailModal');
    }
}

// Quản lý tabs trong modal vật tư
function showTab(tabName) {
    // Cập nhật active tab
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const content = document.getElementById('materialContent');

    switch(tabName) {
        case 'inventory':
            showInventoryTab(content);
            break;
        case 'suppliers':
            showSuppliersTab(content);
            break;
        case 'bom':
            showBOMTab(content);
            break;
    }
}

// Hiển thị tab tồn kho
function showInventoryTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="inventory-header">
                <h4>Tồn kho vật tư</h4>
                <button class="btn btn-primary btn-sm" onclick="addMaterial()">
                    <i class="fas fa-plus"></i> Nhập kho
                </button>
            </div>
            <table class="inventory-table">
                <thead>
                    <tr>
                        <th>Mã VT</th>
                        <th>Tên vật tư</th>
                        <th>Tồn kho</th>
                        <th>Tồn kho tối thiểu</th>
                        <th>Đơn vị</th>
                        <th>Nhà cung cấp</th>
                        <th>Trạng thái</th>
                    </tr>
                </thead>
                <tbody>
                    ${currentData.materials.map(material => {
                        const isLowStock = material.stock <= material.minStock;
                        return `
                            <tr class="${isLowStock ? 'low-stock' : ''}">
                                <td>${material.id}</td>
                                <td>${material.name}</td>
                                <td>${material.stock}</td>
                                <td>${material.minStock}</td>
                                <td>${material.unit}</td>
                                <td>${material.supplier}</td>
                                <td>
                                    ${isLowStock ?
                                        '<span class="status-warning">Sắp hết</span>' :
                                        '<span class="status-ok">Đủ</span>'
                                    }
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// Hiển thị tab nhà cung cấp
function showSuppliersTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="suppliers-header">
                <h4>Danh sách nhà cung cấp</h4>
                <button class="btn btn-primary btn-sm" onclick="addSupplier()">
                    <i class="fas fa-plus"></i> Thêm NCC
                </button>
            </div>
            <div class="suppliers-grid">
                ${currentData.suppliers.map(supplier => `
                    <div class="supplier-card">
                        <h5>${supplier.name}</h5>
                        <p><strong>Liên hệ:</strong> ${supplier.contact}</p>
                        <p><strong>Điện thoại:</strong> ${supplier.phone}</p>
                        <p><strong>Email:</strong> ${supplier.email}</p>
                        <p><strong>Địa chỉ:</strong> ${supplier.address}</p>
                        <div class="supplier-materials">
                            <strong>Vật tư cung cấp:</strong>
                            <ul>
                                ${supplier.materials.map(material => `<li>${material}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Hiển thị tab BOM
function showBOMTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <h4>BOM Templates - Định mức chuẩn</h4>
            <div class="bom-templates">
                ${Object.entries(currentData.bomTemplates).map(([productType, bom]) => `
                    <div class="bom-template-card">
                        <h5>${getProductName(productType)}</h5>
                        <table class="bom-table">
                            <thead>
                                <tr>
                                    <th>Vật tư</th>
                                    <th>Định mức/SP</th>
                                    <th>Đơn vị</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${bom.map(item => `
                                    <tr>
                                        <td>${item.material}</td>
                                        <td>${item.quantity}</td>
                                        <td>${item.unit}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Hiển thị báo cáo
function showReports() {
    alert('Chức năng báo cáo đang được phát triển...');
}

// Hiển thị thông báo
function showNotification(message) {
    // Tạo element thông báo
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;

    // Thêm vào body
    document.body.appendChild(notification);

    // Hiển thị
    setTimeout(() => notification.classList.add('show'), 100);

    // Ẩn sau 3 giây
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Placeholder functions
function addMaterial() {
    alert('Chức năng nhập kho đang được phát triển...');
}

function addSupplier() {
    alert('Chức năng thêm nhà cung cấp đang được phát triển...');
}
