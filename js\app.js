// Biến global
let currentData = getData();
let draggedElement = null;

// Khởi tạo ứng dụng
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    updateStats();
    renderKanbanBoard();
    setupEventListeners();
}

// Cập nhật thống kê
function updateStats() {
    const orders = currentData.orders;
    const totalOrders = orders.length;
    const inProgress = orders.filter(o => ['planning', 'approved', 'cutting', 'printing', 'sewing', 'qc'].includes(o.status)).length;
    const completed = orders.filter(o => o.status === 'completed').length;
    const delayed = orders.filter(o => new Date(o.deliveryDate) < new Date() && o.status !== 'completed').length;

    document.getElementById('totalOrders').textContent = totalOrders;
    document.getElementById('inProgress').textContent = inProgress;
    document.getElementById('completed').textContent = completed;
    document.getElementById('delayed').textContent = delayed;
}

// Render Kanban Board
function renderKanbanBoard() {
    const statuses = ['new', 'planning', 'approved', 'cutting', 'printing', 'sewing', 'qc', 'completed'];

    statuses.forEach(status => {
        const orders = currentData.orders.filter(order => order.status === status);
        const columnContent = document.getElementById(`column-${status}`);
        const countElement = document.getElementById(`count-${status}`);

        // Cập nhật số lượng
        countElement.textContent = orders.length;

        // Render cards
        columnContent.innerHTML = '';
        orders.forEach(order => {
            const card = createOrderCard(order);
            columnContent.appendChild(card);
        });
    });
}

// Tạo card đơn hàng
function createOrderCard(order) {
    const card = document.createElement('div');
    card.className = 'order-card';
    card.draggable = true;
    card.dataset.orderId = order.id;
    
    const priorityClass = order.priority;
    const isDelayed = new Date(order.deliveryDate) < new Date() && order.status !== 'completed';
    
    card.innerHTML = `
        <div class="order-header">
            <span class="order-code">${order.code}</span>
            <span class="priority ${priorityClass}">${getPriorityText(order.priority)}</span>
        </div>
        <div class="customer-name">${order.customer}</div>
        <div class="product-info">
            <strong>${order.product}</strong> - 
            <span class="quantity">${order.quantity} sản phẩm</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: ${order.progress}%"></div>
        </div>
        <div class="order-footer">
            <span class="delivery-date ${isDelayed ? 'text-danger' : ''}">
                <i class="fas fa-calendar"></i> ${formatDate(order.deliveryDate)}
            </span>
            ${order.assignedTo ? `<span class="assigned-to">${order.assignedTo}</span>` : ''}
        </div>
    `;
    
    // Thêm event listeners
    card.addEventListener('click', () => showOrderDetail(order.id));
    card.addEventListener('dragstart', handleDragStart);
    card.addEventListener('dragend', handleDragEnd);
    
    return card;
}

// Xử lý drag and drop
function handleDragStart(e) {
    draggedElement = e.target;
    e.target.classList.add('dragging');
}

function handleDragEnd(e) {
    e.target.classList.remove('dragging');
    draggedElement = null;
}

// Setup event listeners
function setupEventListeners() {
    // Form submit
    document.getElementById('orderForm').addEventListener('submit', handleOrderSubmit);
    
    // Drag and drop cho columns
    const columns = document.querySelectorAll('.column-content');
    columns.forEach(column => {
        column.addEventListener('dragover', handleDragOver);
        column.addEventListener('drop', handleDrop);
        column.addEventListener('dragenter', handleDragEnter);
        column.addEventListener('dragleave', handleDragLeave);
    });
}

function handleDragOver(e) {
    e.preventDefault();
}

function handleDragEnter(e) {
    e.preventDefault();
    e.target.classList.add('drag-over');
}

function handleDragLeave(e) {
    e.target.classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.target.classList.remove('drag-over');
    
    if (draggedElement) {
        const orderId = draggedElement.dataset.orderId;
        const newStatus = e.target.closest('.kanban-column').dataset.status;
        
        updateOrderStatus(orderId, newStatus);
    }
}

// Cập nhật trạng thái đơn hàng
function updateOrderStatus(orderId, newStatus) {
    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        currentData.orders[orderIndex].status = newStatus;
        
        // Cập nhật progress dựa trên status
        const progressMap = {
            'new': 0,
            'planning': 5,
            'approved': 10,
            'cutting': 25,
            'printing': 45,
            'sewing': 65,
            'qc': 85,
            'completed': 100
        };
        currentData.orders[orderIndex].progress = progressMap[newStatus] || 0;
        
        saveData(currentData);
        updateStats();
        renderKanbanBoard();
        
        // Hiển thị thông báo
        showNotification(`Đã chuyển đơn hàng ${currentData.orders[orderIndex].code} sang ${getStatusText(newStatus)}`);
    }
}

// Xử lý submit form tạo đơn hàng
function handleOrderSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const newOrder = {
        id: 'DH' + String(Date.now()).slice(-6),
        code: formData.get('orderCode') || 'DH' + String(Date.now()).slice(-6),
        customer: formData.get('customerName'),
        product: getProductName(formData.get('productType')),
        quantity: parseInt(formData.get('quantity')),
        deliveryDate: formData.get('deliveryDate'),
        status: 'new',
        priority: 'medium',
        assignedTo: '',
        progress: 0,
        notes: formData.get('notes') || '',
        createdDate: new Date().toISOString().split('T')[0],
        materialPlan: null,
        approvals: [],
        bom: generateBOM(formData.get('productType'), parseInt(formData.get('quantity')))
    };
    
    currentData.orders.push(newOrder);
    saveData(currentData);
    
    // Reset form và đóng modal
    e.target.reset();
    closeModal('orderModal');
    
    // Cập nhật giao diện
    updateStats();
    renderKanbanBoard();
    
    showNotification(`Đã tạo đơn hàng ${newOrder.code} thành công!`);
}

// Tạo BOM cho đơn hàng
function generateBOM(productType, quantity) {
    const template = currentData.bomTemplates[productType];
    if (!template) return [];

    return template.map(item => ({
        material: item.material,
        quantity: item.quantity * quantity,
        unit: item.unit
    }));
}

// Utility functions
function getPriorityText(priority) {
    const priorityMap = {
        'high': 'Cao',
        'medium': 'Trung bình',
        'low': 'Thấp'
    };
    return priorityMap[priority] || priority;
}

function getStatusText(status) {
    const statusMap = {
        'new': 'Đơn hàng mới',
        'planning': 'Lập kế hoạch',
        'approved': 'Đã duyệt',
        'cutting': 'Tổ cắt',
        'printing': 'In/Thêu',
        'sewing': 'Tổ may',
        'qc': 'QC',
        'completed': 'Hoàn thành'
    };
    return statusMap[status] || status;
}

function getProductName(productType) {
    const productMap = {
        'ao-thun': 'Áo thun',
        'ao-polo': 'Áo polo',
        'ao-hoodie': 'Áo hoodie',
        'quan-jean': 'Quần jean'
    };
    return productMap[productType] || productType;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
}

// Modal functions
function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Khởi tạo tab mặc định cho các modal
    if (modalId === 'materialModal') {
        setTimeout(() => {
            document.querySelector('.material-tabs .tab-btn.active').click();
        }, 100);
    } else if (modalId === 'planModal') {
        setTimeout(() => {
            document.querySelector('.plan-tabs .tab-btn.active').click();
        }, 100);
    } else if (modalId === 'approvalModal') {
        setTimeout(() => {
            document.querySelector('.approval-tabs .tab-btn.active').click();
        }, 100);
    }
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Đóng modal khi click outside
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
}

// Hiển thị chi tiết đơn hàng
function showOrderDetail(orderId) {
    const order = currentData.orders.find(o => o.id === orderId);
    if (!order) return;

    const content = document.getElementById('orderDetailContent');
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="order-detail-header">
                <h3>Đơn hàng: ${order.code}</h3>
                <span class="priority ${order.priority}">${getPriorityText(order.priority)}</span>
            </div>

            <div class="detail-section">
                <h4>Thông tin cơ bản</h4>
                <div class="detail-grid">
                    <div><strong>Khách hàng:</strong> ${order.customer}</div>
                    <div><strong>Sản phẩm:</strong> ${order.product}</div>
                    <div><strong>Số lượng:</strong> ${order.quantity}</div>
                    <div><strong>Ngày giao:</strong> ${formatDate(order.deliveryDate)}</div>
                    <div><strong>Trạng thái:</strong> ${getStatusText(order.status)}</div>
                    <div><strong>Tiến độ:</strong> ${order.progress}%</div>
                </div>
            </div>

            <div class="detail-section">
                <h4>BOM - Định mức vật tư</h4>
                <table class="bom-table">
                    <thead>
                        <tr>
                            <th>Vật tư</th>
                            <th>Số lượng</th>
                            <th>Đơn vị</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${order.bom.map(item => `
                            <tr>
                                <td>${item.material}</td>
                                <td>${item.quantity}</td>
                                <td>${item.unit}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="detail-section">
                <h4>Giao việc</h4>
                <div class="assign-section">
                    <select id="assignWorker" class="form-control">
                        <option value="">Chọn nhân viên/đối tác</option>
                        ${currentData.workers.map(worker => `
                            <option value="${worker.name}" ${order.assignedTo === worker.name ? 'selected' : ''}>
                                ${worker.name} - ${worker.role}
                            </option>
                        `).join('')}
                    </select>
                    <button class="btn btn-primary" onclick="assignWorker('${order.id}')">
                        Giao việc
                    </button>
                </div>
            </div>

            ${order.notes ? `
                <div class="detail-section">
                    <h4>Ghi chú</h4>
                    <p>${order.notes}</p>
                </div>
            ` : ''}
        </div>
    `;

    openModal('orderDetailModal');
}

// Giao việc cho nhân viên
function assignWorker(orderId) {
    const workerSelect = document.getElementById('assignWorker');
    const selectedWorker = workerSelect.value;

    if (!selectedWorker) {
        alert('Vui lòng chọn nhân viên/đối tác');
        return;
    }

    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        currentData.orders[orderIndex].assignedTo = selectedWorker;
        saveData(currentData);
        renderKanbanBoard();
        showNotification(`Đã giao việc cho ${selectedWorker}`);
        closeModal('orderDetailModal');
    }
}

// Quản lý tabs trong modal vật tư
function showTab(tabName) {
    // Cập nhật active tab
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const content = document.getElementById('materialContent');

    switch(tabName) {
        case 'inventory':
            showInventoryTab(content);
            break;
        case 'suppliers':
            showSuppliersTab(content);
            break;
        case 'bom':
            showBOMTab(content);
            break;
    }
}

// Hiển thị tab tồn kho
function showInventoryTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="inventory-header">
                <h4>Tồn kho vật tư</h4>
                <button class="btn btn-primary btn-sm" onclick="addMaterial()">
                    <i class="fas fa-plus"></i> Nhập kho
                </button>
            </div>
            <table class="inventory-table">
                <thead>
                    <tr>
                        <th>Mã VT</th>
                        <th>Tên vật tư</th>
                        <th>Tồn kho</th>
                        <th>Tồn kho tối thiểu</th>
                        <th>Đơn vị</th>
                        <th>Nhà cung cấp</th>
                        <th>Trạng thái</th>
                    </tr>
                </thead>
                <tbody>
                    ${currentData.materials.map(material => {
                        const isLowStock = material.stock <= material.minStock;
                        return `
                            <tr class="${isLowStock ? 'low-stock' : ''}">
                                <td>${material.id}</td>
                                <td>${material.name}</td>
                                <td>${material.stock}</td>
                                <td>${material.minStock}</td>
                                <td>${material.unit}</td>
                                <td>${material.supplier}</td>
                                <td>
                                    ${isLowStock ?
                                        '<span class="status-warning">Sắp hết</span>' :
                                        '<span class="status-ok">Đủ</span>'
                                    }
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// Hiển thị tab nhà cung cấp
function showSuppliersTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <div class="suppliers-header">
                <h4>Danh sách nhà cung cấp</h4>
                <button class="btn btn-primary btn-sm" onclick="addSupplier()">
                    <i class="fas fa-plus"></i> Thêm NCC
                </button>
            </div>
            <div class="suppliers-grid">
                ${currentData.suppliers.map(supplier => `
                    <div class="supplier-card">
                        <h5>${supplier.name}</h5>
                        <p><strong>Liên hệ:</strong> ${supplier.contact}</p>
                        <p><strong>Điện thoại:</strong> ${supplier.phone}</p>
                        <p><strong>Email:</strong> ${supplier.email}</p>
                        <p><strong>Địa chỉ:</strong> ${supplier.address}</p>
                        <div class="supplier-materials">
                            <strong>Vật tư cung cấp:</strong>
                            <ul>
                                ${supplier.materials.map(material => `<li>${material}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Hiển thị tab BOM
function showBOMTab(content) {
    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <h4>BOM Templates - Định mức chuẩn</h4>
            <div class="bom-templates">
                ${Object.entries(currentData.bomTemplates).map(([productType, bom]) => `
                    <div class="bom-template-card">
                        <h5>${getProductName(productType)}</h5>
                        <table class="bom-table">
                            <thead>
                                <tr>
                                    <th>Vật tư</th>
                                    <th>Định mức/SP</th>
                                    <th>Đơn vị</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${bom.map(item => `
                                    <tr>
                                        <td>${item.material}</td>
                                        <td>${item.quantity}</td>
                                        <td>${item.unit}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Hiển thị báo cáo
function showReports() {
    alert('Chức năng báo cáo đang được phát triển...');
}

// Hiển thị thông báo
function showNotification(message) {
    // Tạo element thông báo
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <span>${message}</span>
    `;

    // Thêm vào body
    document.body.appendChild(notification);

    // Hiển thị
    setTimeout(() => notification.classList.add('show'), 100);

    // Ẩn sau 3 giây
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

// Placeholder functions
function addMaterial() {
    alert('Chức năng nhập kho đang được phát triển...');
}

function addSupplier() {
    alert('Chức năng thêm nhà cung cấp đang được phát triển...');
}

// Quản lý kế hoạch vật tư
function showPlanTab(tabName) {
    // Cập nhật active tab
    document.querySelectorAll('.plan-tabs .tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const content = document.getElementById('planContent');

    switch(tabName) {
        case 'pending':
            showPendingPlanTab(content);
            break;
        case 'planned':
            showPlannedTab(content);
            break;
        case 'materials':
            showMaterialSummaryTab(content);
            break;
    }
}

// Tab đơn hàng chờ lập kế hoạch
function showPendingPlanTab(content) {
    const pendingOrders = currentData.orders.filter(o => o.status === 'new');

    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <h4>Đơn hàng chờ lập kế hoạch vật tư</h4>
            <div class="pending-orders">
                ${pendingOrders.map(order => `
                    <div class="plan-order-card">
                        <div class="plan-order-header">
                            <h5>${order.code} - ${order.customer}</h5>
                            <span class="priority ${order.priority}">${getPriorityText(order.priority)}</span>
                        </div>
                        <div class="plan-order-info">
                            <p><strong>Sản phẩm:</strong> ${order.product}</p>
                            <p><strong>Số lượng:</strong> ${order.quantity}</p>
                            <p><strong>Ngày giao:</strong> ${formatDate(order.deliveryDate)}</p>
                        </div>
                        <div class="plan-order-actions">
                            <button class="btn btn-primary btn-sm" onclick="createMaterialPlan('${order.id}')">
                                <i class="fas fa-clipboard-list"></i> Lập kế hoạch vật tư
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Tab đã lập kế hoạch
function showPlannedTab(content) {
    const plannedOrders = currentData.orders.filter(o => o.materialPlan);

    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <h4>Đơn hàng đã lập kế hoạch vật tư</h4>
            <div class="planned-orders">
                ${plannedOrders.map(order => `
                    <div class="plan-order-card">
                        <div class="plan-order-header">
                            <h5>${order.code} - ${order.customer}</h5>
                            <span class="status-badge ${order.materialPlan.status}">
                                ${order.materialPlan.status === 'approved' ? 'Đã duyệt' : 'Chờ duyệt'}
                            </span>
                        </div>
                        <div class="plan-order-info">
                            <p><strong>Người lập:</strong> ${order.materialPlan.planner}</p>
                            <p><strong>Ngày lập:</strong> ${formatDate(order.materialPlan.planDate)}</p>
                        </div>
                        <div class="material-plan-summary">
                            <h6>Kế hoạch vật tư:</h6>
                            <table class="mini-table">
                                <thead>
                                    <tr><th>Vật tư</th><th>Cần</th><th>Có</th><th>Thiếu</th></tr>
                                </thead>
                                <tbody>
                                    ${order.materialPlan.materials.map(m => `
                                        <tr class="${m.shortage > 0 ? 'shortage' : ''}">
                                            <td>${m.material}</td>
                                            <td>${m.required}</td>
                                            <td>${m.available}</td>
                                            <td>${m.shortage}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <div class="plan-order-actions">
                            <button class="btn btn-info btn-sm" onclick="editMaterialPlan('${order.id}')">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </button>
                            ${order.materialPlan.status === 'draft' ? `
                                <button class="btn btn-success btn-sm" onclick="submitForApproval('${order.id}')">
                                    <i class="fas fa-paper-plane"></i> Gửi duyệt
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Tạo kế hoạch vật tư
function createMaterialPlan(orderId) {
    const order = currentData.orders.find(o => o.id === orderId);
    if (!order) return;

    // Tính toán vật tư cần thiết
    const materials = order.bom.map(bomItem => {
        const material = currentData.materials.find(m => m.name === bomItem.material);
        const required = bomItem.quantity;
        const available = material ? material.stock : 0;
        const shortage = Math.max(0, required - available);

        return {
            material: bomItem.material,
            required: required,
            available: available,
            shortage: shortage,
            supplier: material ? material.supplier : 'Chưa xác định'
        };
    });

    // Tạo kế hoạch vật tư
    const materialPlan = {
        planDate: new Date().toISOString().split('T')[0],
        planner: 'Nguyễn Thị Lan', // Hardcode cho demo
        status: 'draft',
        materials: materials
    };

    // Cập nhật đơn hàng
    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    currentData.orders[orderIndex].materialPlan = materialPlan;
    currentData.orders[orderIndex].status = 'planning';
    currentData.orders[orderIndex].assignedTo = 'PKD - Nguyễn Thị Lan';

    saveData(currentData);
    updateStats();
    renderKanbanBoard();
    showPlanTab('planned');

    showNotification(`Đã tạo kế hoạch vật tư cho đơn hàng ${order.code}`);
}

// Gửi kế hoạch để phê duyệt
function submitForApproval(orderId) {
    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        currentData.orders[orderIndex].materialPlan.status = 'pending_approval';
        saveData(currentData);
        showPlanTab('planned');
        showNotification('Đã gửi kế hoạch vật tư để phê duyệt');
    }
}

// Quản lý phê duyệt
function showApprovalTab(tabName) {
    // Cập nhật active tab
    document.querySelectorAll('.approval-tabs .tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    const content = document.getElementById('approvalContent');

    switch(tabName) {
        case 'pending':
            showPendingApprovalTab(content);
            break;
        case 'approved':
            showApprovedTab(content);
            break;
        case 'rejected':
            showRejectedTab(content);
            break;
    }
}

// Tab chờ phê duyệt
function showPendingApprovalTab(content) {
    const pendingApproval = currentData.orders.filter(o =>
        o.materialPlan && o.materialPlan.status === 'pending_approval'
    );

    content.innerHTML = `
        <div style="padding: 1.5rem;">
            <h4>Kế hoạch vật tư chờ phê duyệt</h4>
            <div class="approval-orders">
                ${pendingApproval.map(order => `
                    <div class="approval-order-card">
                        <div class="approval-order-header">
                            <h5>${order.code} - ${order.customer}</h5>
                            <span class="priority ${order.priority}">${getPriorityText(order.priority)}</span>
                        </div>
                        <div class="approval-order-info">
                            <p><strong>Người lập:</strong> ${order.materialPlan.planner}</p>
                            <p><strong>Ngày lập:</strong> ${formatDate(order.materialPlan.planDate)}</p>
                            <p><strong>Ngày giao:</strong> ${formatDate(order.deliveryDate)}</p>
                        </div>
                        <div class="material-plan-detail">
                            <h6>Chi tiết kế hoạch vật tư:</h6>
                            <table class="approval-table">
                                <thead>
                                    <tr>
                                        <th>Vật tư</th>
                                        <th>Cần</th>
                                        <th>Tồn kho</th>
                                        <th>Thiếu</th>
                                        <th>Nhà cung cấp</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${order.materialPlan.materials.map(m => `
                                        <tr class="${m.shortage > 0 ? 'shortage' : ''}">
                                            <td>${m.material}</td>
                                            <td>${m.required}</td>
                                            <td>${m.available}</td>
                                            <td>${m.shortage}</td>
                                            <td>${m.supplier}</td>
                                            <td>
                                                ${m.shortage > 0 ?
                                                    '<span class="status-warning">Cần mua thêm</span>' :
                                                    '<span class="status-ok">Đủ</span>'
                                                }
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        <div class="approval-actions">
                            <button class="btn btn-success btn-sm" onclick="approveOrder('${order.id}')">
                                <i class="fas fa-check"></i> Phê duyệt
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="rejectOrder('${order.id}')">
                                <i class="fas fa-times"></i> Từ chối
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="requestRevision('${order.id}')">
                                <i class="fas fa-edit"></i> Yêu cầu chỉnh sửa
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Phê duyệt đơn hàng
function approveOrder(orderId) {
    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        const order = currentData.orders[orderIndex];

        // Cập nhật trạng thái
        order.materialPlan.status = 'approved';
        order.status = 'approved';
        order.assignedTo = '';

        // Thêm vào lịch sử phê duyệt
        order.approvals.push({
            stage: 'material_plan',
            approver: 'Trưởng phòng PKD',
            date: new Date().toISOString().split('T')[0],
            status: 'approved',
            notes: 'Kế hoạch vật tư được phê duyệt'
        });

        saveData(currentData);
        updateStats();
        renderKanbanBoard();
        showApprovalTab('pending');

        showNotification(`Đã phê duyệt kế hoạch vật tư cho đơn hàng ${order.code}`);
    }
}

// Từ chối đơn hàng
function rejectOrder(orderId) {
    const reason = prompt('Lý do từ chối:');
    if (!reason) return;

    const orderIndex = currentData.orders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        const order = currentData.orders[orderIndex];

        order.materialPlan.status = 'rejected';
        order.status = 'new';
        order.assignedTo = '';

        order.approvals.push({
            stage: 'material_plan',
            approver: 'Trưởng phòng PKD',
            date: new Date().toISOString().split('T')[0],
            status: 'rejected',
            notes: reason
        });

        saveData(currentData);
        updateStats();
        renderKanbanBoard();
        showApprovalTab('pending');

        showNotification(`Đã từ chối kế hoạch vật tư cho đơn hàng ${order.code}`);
    }
}

// Placeholder functions
function editMaterialPlan(orderId) {
    alert('Chức năng chỉnh sửa kế hoạch vật tư đang được phát triển...');
}

function showMaterialSummaryTab(content) {
    alert('Chức năng tổng hợp vật tư đang được phát triển...');
}

function showApprovedTab(content) {
    alert('Chức năng xem đã phê duyệt đang được phát triển...');
}

function showRejectedTab(content) {
    alert('Chức năng xem từ chối đang được phát triển...');
}

function requestRevision(orderId) {
    alert('Chức năng yêu cầu chỉnh sửa đang được phát triển...');
}
