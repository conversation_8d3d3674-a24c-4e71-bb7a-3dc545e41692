// <PERSON><PERSON> liệu mẫu cho demo
const sampleData = {
    // <PERSON>h sách đơn hàng mẫu
    orders: [
        {
            id: 'DH001',
            code: 'DH001',
            customer: 'Công ty TNHH ABC',
            product: 'Áo thun cotton',
            quantity: 500,
            deliveryDate: '2024-02-15',
            status: 'new',
            priority: 'high',
            assignedTo: '',
            progress: 0,
            notes: '<PERSON><PERSON>u cầu chất lượng cao, in logo công ty',
            createdDate: '2024-01-15',
            materialPlan: null,
            approvals: [],
            bom: [
                { material: 'Vải cotton 100%', quantity: 250, unit: 'm' },
                { material: 'Chỉ may', quantity: 10, unit: 'cuộn' },
                { material: 'Nhãn mác', quantity: 500, unit: 'cái' },
                { material: 'Mực in', quantity: 2, unit: 'lít' }
            ]
        },
        {
            id: 'DH002',
            code: 'DH002',
            customer: 'Cửa hàng XYZ',
            product: 'Áo polo',
            quantity: 200,
            deliveryDate: '2024-02-20',
            status: 'planning',
            priority: 'medium',
            assignedTo: 'PKD - Nguyễn Thị <PERSON>',
            progress: 10,
            notes: '<PERSON>àu xanh navy, size đa dạng',
            createdDate: '2024-01-10',
            materialPlan: {
                planDate: '2024-01-16',
                planner: 'Nguyễn Thị Lan',
                status: 'draft',
                materials: [
                    { material: 'Vải polo', required: 120, available: 800, shortage: 0, supplier: 'Công ty Vải XYZ' },
                    { material: 'Cúc áo', required: 600, available: 5000, shortage: 0, supplier: 'Công ty Phụ liệu MNO' },
                    { material: 'Chỉ may', required: 8, available: 200, shortage: 0, supplier: 'Công ty Phụ liệu JKL' }
                ]
            },
            approvals: [],
            bom: [
                { material: 'Vải polo', quantity: 120, unit: 'm' },
                { material: 'Cúc áo', quantity: 600, unit: 'cái' },
                { material: 'Chỉ may', quantity: 8, unit: 'cuộn' }
            ]
        },
        {
            id: 'DH003',
            code: 'DH003',
            customer: 'Trường THPT DEF',
            product: 'Áo hoodie',
            quantity: 300,
            deliveryDate: '2024-02-25',
            status: 'approved',
            priority: 'low',
            assignedTo: '',
            progress: 15,
            notes: 'In logo trường, màu đỏ',
            createdDate: '2024-01-05',
            materialPlan: {
                planDate: '2024-01-12',
                planner: 'Nguyễn Thị Lan',
                status: 'approved',
                materials: [
                    { material: 'Vải nỉ', required: 180, available: 600, shortage: 0, supplier: 'Công ty Vải DEF' },
                    { material: 'Dây rút', required: 300, available: 1000, shortage: 0, supplier: 'Công ty Phụ liệu MNO' },
                    { material: 'Mực in', required: 1.5, available: 50, shortage: 0, supplier: 'Công ty Hóa chất PQR' }
                ]
            },
            approvals: [
                { stage: 'material_plan', approver: 'Trưởng phòng PKD', date: '2024-01-13', status: 'approved', notes: 'Kế hoạch vật tư hợp lý' }
            ],
            bom: [
                { material: 'Vải nỉ', quantity: 180, unit: 'm' },
                { material: 'Dây rút', quantity: 300, unit: 'sợi' },
                { material: 'Mực in', quantity: 1.5, unit: 'lít' }
            ]
        },
        {
            id: 'DH004',
            code: 'DH004',
            customer: 'Nhà hàng GHI',
            product: 'Áo đồng phục',
            quantity: 150,
            deliveryDate: '2024-02-18',
            status: 'sewing',
            priority: 'high',
            assignedTo: 'Lê Văn C',
            progress: 75,
            notes: 'Màu trắng, thêu tên nhà hàng',
            createdDate: '2024-01-08',
            bom: [
                { material: 'Vải kaki', quantity: 90, unit: 'm' },
                { material: 'Chỉ thêu', quantity: 5, unit: 'cuộn' },
                { material: 'Cúc áo', quantity: 450, unit: 'cái' }
            ]
        },
        {
            id: 'DH005',
            code: 'DH005',
            customer: 'Công ty JKL',
            product: 'Quần jean',
            quantity: 100,
            deliveryDate: '2024-02-12',
            status: 'qc',
            priority: 'medium',
            assignedTo: 'Phạm Thị D',
            progress: 90,
            notes: 'Size 28-34, màu xanh đậm',
            createdDate: '2024-01-03',
            bom: [
                { material: 'Vải jean', quantity: 80, unit: 'm' },
                { material: 'Khóa kéo', quantity: 100, unit: 'cái' },
                { material: 'Nút jean', quantity: 100, unit: 'cái' }
            ]
        }
    ],

    // Danh sách nhân viên/đối tác
    workers: [
        { id: 'NV001', name: 'Nguyễn Văn A', role: 'Tổ trưởng cắt', department: 'cutting' },
        { id: 'NV002', name: 'Trần Thị B', role: 'Chuyên viên in', department: 'printing' },
        { id: 'NV003', name: 'Lê Văn C', role: 'Tổ trưởng may', department: 'sewing' },
        { id: 'NV004', name: 'Phạm Thị D', role: 'QC', department: 'qc' },
        { id: 'DT001', name: 'Xưởng may ABC', role: 'Đối tác gia công', department: 'external' },
        { id: 'DT002', name: 'Xưởng in XYZ', role: 'Đối tác in ấn', department: 'external' }
    ],

    // Danh sách vật tư
    materials: [
        { id: 'VT001', name: 'Vải cotton 100%', unit: 'm', stock: 1500, minStock: 200, supplier: 'Công ty Vải ABC' },
        { id: 'VT002', name: 'Vải polo', unit: 'm', stock: 800, minStock: 150, supplier: 'Công ty Vải XYZ' },
        { id: 'VT003', name: 'Vải nỉ', unit: 'm', stock: 600, minStock: 100, supplier: 'Công ty Vải DEF' },
        { id: 'VT004', name: 'Vải kaki', unit: 'm', stock: 400, minStock: 80, supplier: 'Công ty Vải ABC' },
        { id: 'VT005', name: 'Vải jean', unit: 'm', stock: 300, minStock: 50, supplier: 'Công ty Vải GHI' },
        { id: 'VT006', name: 'Chỉ may', unit: 'cuộn', stock: 200, minStock: 50, supplier: 'Công ty Phụ liệu JKL' },
        { id: 'VT007', name: 'Chỉ thêu', unit: 'cuộn', stock: 100, minStock: 20, supplier: 'Công ty Phụ liệu JKL' },
        { id: 'VT008', name: 'Cúc áo', unit: 'cái', stock: 5000, minStock: 1000, supplier: 'Công ty Phụ liệu MNO' },
        { id: 'VT009', name: 'Khóa kéo', unit: 'cái', stock: 800, minStock: 200, supplier: 'Công ty Phụ liệu MNO' },
        { id: 'VT010', name: 'Mực in', unit: 'lít', stock: 50, minStock: 10, supplier: 'Công ty Hóa chất PQR' }
    ],

    // Danh sách nhà cung cấp
    suppliers: [
        {
            id: 'NCC001',
            name: 'Công ty Vải ABC',
            contact: 'Nguyễn Văn X',
            phone: '0123456789',
            email: '<EMAIL>',
            address: '123 Đường ABC, Quận 1, TP.HCM',
            materials: ['Vải cotton 100%', 'Vải kaki']
        },
        {
            id: 'NCC002',
            name: 'Công ty Vải XYZ',
            contact: 'Trần Thị Y',
            phone: '0987654321',
            email: '<EMAIL>',
            address: '456 Đường XYZ, Quận 2, TP.HCM',
            materials: ['Vải polo']
        },
        {
            id: 'NCC003',
            name: 'Công ty Phụ liệu JKL',
            contact: 'Lê Văn Z',
            phone: '0369852147',
            email: '<EMAIL>',
            address: '789 Đường JKL, Quận 3, TP.HCM',
            materials: ['Chỉ may', 'Chỉ thêu']
        }
    ],

    // BOM templates cho các sản phẩm
    bomTemplates: {
        'ao-thun': [
            { material: 'Vải cotton 100%', quantity: 0.5, unit: 'm' },
            { material: 'Chỉ may', quantity: 0.02, unit: 'cuộn' },
            { material: 'Nhãn mác', quantity: 1, unit: 'cái' }
        ],
        'ao-polo': [
            { material: 'Vải polo', quantity: 0.6, unit: 'm' },
            { material: 'Cúc áo', quantity: 3, unit: 'cái' },
            { material: 'Chỉ may', quantity: 0.04, unit: 'cuộn' }
        ],
        'ao-hoodie': [
            { material: 'Vải nỉ', quantity: 0.8, unit: 'm' },
            { material: 'Dây rút', quantity: 1, unit: 'sợi' },
            { material: 'Chỉ may', quantity: 0.06, unit: 'cuộn' }
        ],
        'quan-jean': [
            { material: 'Vải jean', quantity: 0.8, unit: 'm' },
            { material: 'Khóa kéo', quantity: 1, unit: 'cái' },
            { material: 'Nút jean', quantity: 1, unit: 'cái' },
            { material: 'Chỉ may', quantity: 0.05, unit: 'cuộn' }
        ]
    }
};

// Lưu dữ liệu vào localStorage nếu chưa có
function initializeData() {
    if (!localStorage.getItem('productionData')) {
        localStorage.setItem('productionData', JSON.stringify(sampleData));
    }
}

// Lấy dữ liệu từ localStorage
function getData() {
    const data = localStorage.getItem('productionData');
    return data ? JSON.parse(data) : sampleData;
}

// Lưu dữ liệu vào localStorage
function saveData(data) {
    localStorage.setItem('productionData', JSON.stringify(data));
}

// Khởi tạo dữ liệu khi load trang
initializeData();
