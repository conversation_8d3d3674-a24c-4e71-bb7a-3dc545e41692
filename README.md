# Hệ thống Quản lý Sản xuất May mặc - Wireframe Demo

## Tổng quan

Đây là wireframe demo cho hệ thống quản lý sản xuất may mặc với giao diện Kanban giống Trello. <PERSON>ệ thống giúp quản lý toàn bộ quy trình sản xuất từ nhận đơn hàng đến hoàn thành sản phẩm.

## Tính năng chính

### 🎯 Dashboard Overview
- Thống kê tổng quan: <PERSON><PERSON><PERSON> đơn hàng, đang sản xuất, ho<PERSON><PERSON> thành, trễ hạn
- Kanban board với 8 cột công đoạn:
  - **Đơn hàng mới**: Đơn hàng vừa tạo, chưa lập kế hoạch
  - **Lập kế hoạch**: PKD đang lập kế hoạch vật tư
  - **Đã duyệt**: <PERSON>ế hoạch vật tư đã được phê duyệt
  - **Tổ Cắt**: <PERSON><PERSON>ng đoạn cắt vải theo định mức
  - **In/Thêu**: C<PERSON>ng đoạn in ấn hoặc thêu logo/hình ảnh
  - **Tổ May**: Công đoạn may thành sản phẩm hoàn chỉnh
  - **QC**: Kiểm tra chất lượng sản phẩm
  - **Hoàn thành**: Sản phẩm đã hoàn thiện, sẵn sàng xuất hàng

### 📋 Quản lý đơn hàng
- **Tạo đơn hàng mới**: Nhập thông tin khách hàng, sản phẩm, số lượng, ngày giao
- **Drag & Drop**: Kéo thả đơn hàng giữa các công đoạn để cập nhật tiến độ
- **Chi tiết đơn hàng**: Xem thông tin chi tiết, BOM, giao việc cho nhân viên
- **Theo dõi tiến độ**: Progress bar hiển thị % hoàn thành
- **Ưu tiên**: Phân loại độ ưu tiên (Cao/Trung bình/Thấp)

### 👥 Quản lý nhân sự
- **Giao việc**: Assign đơn hàng cho nhân viên hoặc đối tác gia công
- **Theo dõi phân công**: Xem ai đang làm việc gì
- **Danh sách nhân viên**: Quản lý thông tin nhân viên theo từng tổ

### 🏢 Phòng Kinh Doanh (PKD)
- **Lập kế hoạch vật tư**: Tính toán vật tư cần thiết cho từng đơn hàng
- **Kiểm tra tồn kho**: So sánh nhu cầu với tồn kho hiện có
- **Phát hiện thiếu hụt**: Cảnh báo vật tư cần mua thêm
- **Gửi phê duyệt**: Submit kế hoạch vật tư để lãnh đạo duyệt

### ✅ Quy trình phê duyệt
- **Phê duyệt kế hoạch**: Lãnh đạo duyệt kế hoạch vật tư từ PKD
- **Theo dõi trạng thái**: Chờ duyệt, đã duyệt, từ chối
- **Yêu cầu chỉnh sửa**: Feedback để PKD điều chỉnh kế hoạch
- **Lịch sử phê duyệt**: Lưu trữ toàn bộ quá trình phê duyệt

### 📦 Quản lý vật tư
- **Tồn kho**: Theo dõi số lượng vật tư hiện có
- **Cảnh báo thiếu hàng**: Highlight vật tư sắp hết
- **Nhà cung cấp**: Quản lý thông tin các nhà cung cấp
- **BOM Templates**: Định mức vật tư chuẩn cho từng loại sản phẩm

## Cấu trúc dự án

```
pmqlsx-maymac/
├── index.html          # Trang chính
├── css/
│   └── style.css       # Styling cho toàn bộ ứng dụng
├── js/
│   ├── app.js          # Logic chính của ứng dụng
│   └── data.js         # Dữ liệu mẫu và quản lý localStorage
└── README.md           # Tài liệu hướng dẫn
```

## Hướng dẫn sử dụng

### 1. Khởi chạy ứng dụng
- Mở file `index.html` trong trình duyệt web
- Ứng dụng sẽ tự động load dữ liệu mẫu

### 2. Tạo đơn hàng mới
1. Click nút "Tạo đơn hàng" trên header
2. Điền thông tin: mã đơn, khách hàng, sản phẩm, số lượng, ngày giao
3. Hệ thống tự động tính BOM dựa trên template
4. Đơn hàng mới sẽ xuất hiện ở cột "Đơn hàng mới"

### 3. Quản lý tiến độ sản xuất
1. **Kéo thả đơn hàng**: Drag card đơn hàng từ cột này sang cột khác
2. **Cập nhật tự động**: Progress bar và thống kê sẽ tự động cập nhật
3. **Xem chi tiết**: Click vào card để xem thông tin chi tiết

### 4. Giao việc cho nhân viên
1. Click vào card đơn hàng để mở chi tiết
2. Trong phần "Giao việc", chọn nhân viên/đối tác
3. Click "Giao việc" để assign
4. Tên người được giao sẽ hiển thị trên card

### 5. Quản lý vật tư
1. Click nút "Quản lý vật tư" trên header
2. **Tab Tồn kho**: Xem số lượng vật tư hiện có, cảnh báo thiếu hàng
3. **Tab Nhà cung cấp**: Quản lý thông tin các nhà cung cấp
4. **Tab BOM**: Xem định mức vật tư chuẩn cho từng sản phẩm

## Dữ liệu mẫu

Hệ thống đi kèm với dữ liệu mẫu bao gồm:
- 5 đơn hàng ở các công đoạn khác nhau
- 6 nhân viên/đối tác gia công
- 10 loại vật tư với thông tin tồn kho
- 3 nhà cung cấp
- BOM templates cho 4 loại sản phẩm

## Công nghệ sử dụng

- **HTML5**: Cấu trúc trang web
- **CSS3**: Styling với Flexbox, Grid, Animation
- **Vanilla JavaScript**: Logic ứng dụng, không sử dụng framework
- **Font Awesome**: Icons
- **LocalStorage**: Lưu trữ dữ liệu tạm thời

## Tính năng nổi bật

### 🎨 Giao diện thân thiện
- Thiết kế responsive, hoạt động tốt trên mobile
- Màu sắc trực quan, dễ phân biệt trạng thái
- Animation mượt mà khi drag & drop

### ⚡ Hiệu suất cao
- Không sử dụng framework nặng
- Dữ liệu được cache trong localStorage
- Cập nhật real-time khi thay đổi

### 🔧 Dễ mở rộng
- Code được tổ chức rõ ràng theo module
- Dữ liệu được tách riêng
- Dễ dàng thêm tính năng mới

## Roadmap phát triển

### Phase 1 (Hiện tại)
- ✅ Kanban board cơ bản
- ✅ Quản lý đơn hàng
- ✅ Drag & drop
- ✅ Quản lý vật tư cơ bản

### Phase 2 (Tương lai)
- 🔄 Báo cáo chi tiết
- 🔄 Nhập/xuất kho vật tư
- 🔄 Tính toán chi phí
- 🔄 Tích hợp API backend
- 🔄 Quản lý người dùng và phân quyền

## Liên hệ

Đây là wireframe demo cho mục đích trình bày ý tưởng. Để phát triển thành sản phẩm hoàn chỉnh, cần bổ sung backend API, database và các tính năng bảo mật.

---

**Lưu ý**: Dữ liệu được lưu trong localStorage của trình duyệt. Để reset về dữ liệu mẫu ban đầu, xóa localStorage hoặc refresh trang trong chế độ incognito.
